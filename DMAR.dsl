/*
 * Intel ACPI Component Architecture
 * AML/ASL+ Disassembler version 20250404 (32-bit version)
 * Copyright (c) 2000 - 2025 Intel Corporation
 * 
 * Disassembly of C:/Users/<USER>/Desktop/DMAR.bin
 *
 * ACPI Data Table [DMAR]
 *
 * Format: [HexOffset DecimalOffset ByteLength]  FieldName : FieldValue (in hex)
 */

[000h 0000 004h]                   Signature : "DMAR"    [DMA Remapping Table]
[004h 0004 004h]                Table Length : 00000068
[008h 0008 001h]                    Revision : 01
[009h 0009 001h]                    Checksum : DE     /* Incorrect checksum, should be FE */
[00Ah 0010 006h]                      Oem ID : "ASUS  "
[010h 0016 008h]                Oem Table ID : " XPS1080"
[018h 0024 004h]                Oem Revision : 30300030
[01Ch 0028 004h]             Asl Compiler ID : " MSF"
[020h 0032 004h]       Asl Compiler Revision : 00001354

[024h 0036 001h]          Host Address Width : 01
[025h 0037 001h]                       Flags : 27
[026h 0038 00Ah]                    Reserved : 01 00 00 00 00 00 00 00 00 00

[030h 0048 002h]               Subtable Type : 0000 [Hardware Unit Definition]
[032h 0050 002h]                      Length : 0020

[034h 0052 001h]                       Flags : 00
[035h 0053 001h]        Size (decoded below) : 00
                          Size (pages, log2) : 0
[036h 0054 002h]          PCI Segment Number : 0000
[038h 0056 008h]       Register Base Address : 00000000FED91000

[040h 0064 001h]           Device Scope Type : 01 [PCI Endpoint Device]
[041h 0065 001h]                Entry Length : 06
[042h 0066 001h]                       Flags : 00
[043h 0067 001h]                    Reserved : 00
[044h 0068 001h]              Enumeration ID : 00
[045h 0069 001h]              PCI Bus Number : 00


[046h 0070 001h]           Device Scope Type : 00 [Reserved value]
[047h 0071 001h]                Entry Length : 00
[048h 0072 001h]                       Flags : 00
[049h 0073 001h]                    Reserved : 00
[04Ah 0074 001h]              Enumeration ID : 00
[04Bh 0075 001h]              PCI Bus Number : 00
Invalid zero length subtable

Raw Table Data: Length 104 (0x68)

    0000: 44 4D 41 52 68 00 00 00 01 DE 41 53 55 53 20 20  // DMARh.....ASUS  
    0010: 20 58 50 53 31 30 38 30 30 00 30 30 01 4D 53 46  //  XPS10800.00.MSF
    0020: 54 13 00 00 01 27 01 00 00 00 00 00 00 00 00 00  // T....'..........
    0030: 00 00 20 00 00 00 00 00 00 10 D9 FE 00 00 00 00  // .. .............
    0040: 01 06 00 00 00 00 00 00 00 00 00 00 00 00 00 00  // ................
    0050: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  // ................
    0060: 00 00 00 00 00 00 00 00                          // ........
